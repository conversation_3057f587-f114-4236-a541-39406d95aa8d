# 员工日志管理系统产品需求规格说

## 一、引言

### 1.1 文档目的

本文档明确员工日志管理系统的功能需求、技术规范、权限控制及交互标准，作为系统设计、开发、测试及验收的核心依据，确保系统满足企业对员工工作记录、项目跟踪及数据统计的管理需求。

### 1.2 系统概述

员工日志管理系统是一款基于前后端分离架构的企业级管理工具，支持客户信息、部门信息、员工信息、项目信息的维护，以及工作日志、加班记录的管理与统计。通过权限分级控制数据访问范围，结合全文检索、数据导出等功能，实现工作记录的高效管理与数据价值挖掘。

### 1.3 目标用户

- **管理员**：负责系统配置、人员、部门、项目、客户等信息维护，拥有最高权限，能查看导出到所有人员的日志信息；
- **销售人员**：维护项目信息、提交工作日志及加班记录，查看全员项目及日志信息；
- **普通员工**：提交工作日志及加班记录，维护项目信息，查看全员项目及日志信息。

## 二、总体描述

### 2.1 技术架构

| 模块         | 技术栈            | 依赖管理工具     |
| ------------ | ----------------- | ---------------- |
| 后端         | FastAPI（Python） | uv（包依赖管理） |
| 前端         | Next.js           | npm（依赖管理）  |
| 前端样式     | Tailwind CSS      | -                |
| 界面风格参考 | Ant Design Pro    | -                |

### 2.2 核心业务流程

1. **用户登录流程**：用户输入账号密码（email/员工工号）→系统验证→首次登录强制修改密码→进入系统首页；
2. **日志管理流程**：员工创建 / 编辑工作日志→关联项目 / 客户→提交后存入系统→支持查询 / 导出；
3. **权限控制流程**：系统根据用户角色（管理员 / 普通员工 / 销售人员）分配操作权限→访问数据时校验权限→返回符合权限的数据范围。

## 三、功能需求

### 3.1 登录与个人中心模块

#### 3.1.1 登录功能

- **基础登录**：支持账号（email/员工工号）+ 密码登录，输入错误时提示 “账号或密码错误，请重新输入”；登录成功提示 “登录成功，即将跳转至首页”。
- **首次登录强制改密码**：新用户首次登录时，系统自动跳转至 “密码修改页”，要求输入原密码 + 新密码（需满足 “8-20 位，含数字 + 字母” 规则），未完成修改无法进入系统。
- **密码找回**：支持通过邮件找回密码，流程为 “输入注册邮箱→系统发送验证码→验证通过后重置密码”，邮件包含 “有效期 24 小时的重置链接”。

#### 3.1.2 个人中心

- 支持查看个人基本信息（姓名、部门、角色等）；
- 支持主动修改密码（需验证原密码）；
- 支持绑定 / 修改邮箱（用于密码找回）。

### 3.2 客户信息管理模块

#### 3.2.1 核心功能

- **信息维护**：支持客户信息的新增、编辑、删除（逻辑删除）及查看，字段包括客户名称、客户简称、联系方式、地址、创建时间等；
- **数据查询**：支持按客户名称、客户简称模糊查询；
- **数据导出**：支持将客户列表导出为 Excel 格式；
- **分页展示**：默认每页显示 5 条数据，支持上下翻页及页码跳转。

#### 3.2.2 权限控制

- 所有角色（管理员 / 普通员工 / 销售人员）均可查看客户信息；
- 仅管理员可新增 / 编辑 / 删除客户信息。

### 3.3 部门信息管理模块

#### 3.3.1 核心功能

- **信息维护**：支持部门信息的新增（支持多级部门）、编辑、删除（需先确认该部门无关联员工）及查看，字段包括部门名称、部门编号、上级部门、负责人、创建时间等；
- **数据查询**：支持按部门名称、部门编号模糊查询；
- **数据导出**：支持将部门列表导出为 Excel 格式；
- **分页展示**：默认每页显示 5 条数据，支持上下翻页及页码跳转。

#### 3.3.2 权限控制

- 所有角色均可查看部门信息；
- 仅管理员可新增 / 编辑 / 删除部门信息。

### 3.4 员工信息管理模块

#### 3.4.1 核心功能

- **信息维护**：支持员工信息的新增、编辑、删除（逻辑删除）及查看，字段包括员工姓名、工号、所属部门、角色（管理员 / 普通员工 / 销售人员）、联系方式、入职时间等；
- **数据查询**：支持按员工姓名、工号、所属部门模糊查询；
- **数据导出**：支持将员工列表导出为 Excel 格式；
- **分页展示**：默认每页显示 5 条数据，支持上下翻页及页码跳转。

#### 3.4.2 权限控制

- 所有角色均可查看员工信息；
- 仅管理员可新增 / 编辑 / 删除员工信息。

### 3.5 项目信息管理模块

#### 3.5.1 核心功能

- **信息维护**：支持项目信息的新增、编辑、删除（逻辑删除）及查看，字段包括项目名称、项目号、关联客户、项目负责人、项目状态（未验收 / 已验收）、创建时间等；
- **全文检索**：支持按 “项目名称、客户简称、客户名称、项目号” 全文检索（匹配任意字段关键词即返回结果）；
- **数据导出**：支持将项目列表导出为 Excel 格式；
- **分页展示**：默认每页显示 5 条数据，支持上下翻页及页码跳转。

#### 3.5.2 权限控制

- 所有角色均可查看项目信息；
- 管理员、普通员工、销售人员均可新增 / 编辑 / 删除项目信息。

### 3.6 工作日志管理模块

#### 3.6.1 核心功能

- 个人日志管理：
  - 支持新增工作日志（关联项目、填写工作内容、工作时长等）、编辑（仅可修改自己的日志）、删除（仅可删除自己的日志）及查看；
  - 支持按日期（今日 / 本周 / 本月）筛选个人日志；
  - 只显示自己填写的日志信息
  - **全文检索**：支持按 “项目名称、客户简称、客户名称、项目号、工作内容” 全文检索（跨日志、项目、客户、员工表匹配关键词）；
- 全部日志管理：
  - 支持查看所有员工的工作日志，按员工姓名、日期范围筛选；
  - **全文检索**：支持按 “项目名称、工作内容、客户简称、客户名称、员工姓名、项目号” 全文检索（跨日志、项目、客户、员工表匹配关键词）；
- **数据导出**：支持将日志列表（个人 / 全部）导出为 Excel 格式；
- **分页展示**：默认每页显示 5 条数据，支持上下翻页及页码跳转。

#### 3.6.2 权限控制

- 所有角色均可查看自己及他人的工作日志；
- 员工仅可新增 / 编辑 / 删除自己的工作日志；
- 管理员可编辑 / 删除所有员工的工作日志。

### 3.7 加班记录管理模块

#### 3.7.1 核心功能

- 记录维护：
  - 支持新增加班记录（可关联项目）、编辑（仅可修改自己的记录）、删除（仅可删除自己的记录）及查看；
  - 字段说明：关联员工（默认当前登录人）、加班开始时间、加班结束时间、自动计算加班时长、关联项目、加班原因、备注；
- **数据查询**：支持按加班日期、关联项目（通过关联日志匹配）筛选；
- **数据导出**：支持将加班记录列表导出为 Excel 格式；
- **分页展示**：默认每页显示 5 条数据，支持上下翻页及页码跳转。

#### 3.7.2 权限控制

- 所有角色均可查看自己及他人的加班记录；
- 员工仅可新增 / 编辑 / 删除自己的加班记录；
- 管理员可编辑 / 删除所有员工的加班记录。

### 3.8 汇总统计管理模块

#### 3.8.1 核心功能

- **项目统计**：按项目维度统计关联的日志数量、参与员工数、总工作时长；
- **员工统计**：按员工维度统计指定周期（周 / 月 / 季）的日志数量、加班总时长、参与项目数；
- **部门统计**：按部门维度统计指定周期的总日志量、平均工作时长、加班总时长；
- **数据可视化**：通过柱状图、折线图展示统计结果（Echarts数据看板风格）；
- **数据导出**：支持将统计结果（含图表数据）导出为 Excel 格式。

#### 3.8.2 权限控制

- 所有角色均可查看统计数据（无数据权限限制）。

### 3.9 系统管理模块

#### 3.9.1 核心功能

- **角色管理**：管理员可查看系统角色（管理员 / 普通员工 / 销售人员）及权限配置，支持新增自定义角色（可选权限组合）；
- **日志审计**：记录用户关键操作（如新增员工、删除日志等），包括操作人、操作时间、操作内容、操作结果；
- **系统配置**：设置密码规则（如长度、复杂度）、导出文件格式（默认 Excel）、分页默认条数（支持修改默认 5 条配置）。

#### 3.9.2 权限控制

- 仅管理员可访问系统管理模块。

## 四、非功能需求

### 4.1 性能需求

- 页面加载时间：首次加载≤3 秒，二次加载≤1 秒；
- 全文检索响应时间：单表检索≤1 秒，跨表联合检索≤2 秒；
- 数据导出：1000 条以内数据导出≤3 秒，支持后台异步导出（超过 1000 条时）。

### 4.2 安全需求

- 密码存储：采用 SHA-256 加密存储，禁止明文传输；
- 接口安全：所有接口需验证登录态（Token），敏感操作（如删除数据）需二次确认；
- 数据备份：每日自动备份数据库，备份数据保留 30 天。

### 4.3 兼容性需求

- 浏览器兼容：支持 Chrome（≥90）、Edge（≥90）、Firefox（≥88）最新稳定版；
- 响应式设计：适配 1366×768 及以上分辨率屏幕（含 PC 端、移动端）。

## 五、技术规范

### 5.1 后端技术规范

- 框架：FastAPI（Python 3.10+），遵循 RESTful API 设计规范；
- 数据库：MySQL 8.0+，使用 ORM（如 SQLAlchemy）操作数据；
- 依赖管理：使用 uv 管理依赖；
- 接口文档：自动生成 OpenAPI 文档（FastAPI 自带），支持在线调试。

### 5.2 前端技术规范

- 框架：Next.js
- 组件库：参考 Ant Design Pro 组件风格，基于 Tailwind CSS 自定义样式；
- 依赖管理：使用 npm 管理依赖；
- 代码规范：遵循 ESLint+Prettier 规则，确保代码风格统一。

## 六、界面设计规范

1. **风格参考**：以 Ant Design Pro 为基础，采用 “简洁、高效” 的设计语言，主色调使用蓝色（#1677ff，Ant Design 主题色），辅助色使用灰色系（区分不同层级信息）；
2. **布局规范**：左侧固定导航栏（支持折叠）+ 顶部操作栏（含搜索、用户信息）+ 主内容区（自适应布局）,同时支持移动端和PC端自适应；
3. 组件规范：
   - 表单：使用 Ant Design Pro 表单组件，必填项标注 “*”，错误提示实时显示；
   - 列表：统一使用表格组件，支持排序（点击表头）、筛选（顶部筛选区）；
   - 提示：操作结果（成功 / 失败）使用全局通知组件（右上角浮动提示）。

## 七、附录

### 7.1 权限矩阵表

| 功能模块        | 普通员工 / 销售人员 | 管理员   |
| --------------- | ------------------- | -------- |
| 客户信息 - 查看 | ✅                   | ✅        |
| 客户信息 - 维护 | ❌                   | ✅        |
| 部门信息 - 查看 | ✅                   | ✅        |
| 部门信息 - 维护 | ❌                   | ✅        |
| 员工信息 - 查看 | ✅                   | ✅        |
| 员工信息 - 维护 | ❌                   | ✅        |
| 项目信息 - 查看 | ✅                   | ✅        |
| 项目信息 - 维护 | ✅                   | ✅        |
| 工作日志 - 查看 | 全员可见            | 全员可见 |
| 工作日志 - 维护 | 仅自己              | 全员     |
| 加班记录 - 查看 | 全员可见            | 全员可见 |
| 加班记录 - 维护 | 仅自己              | 全员     |
| 汇总统计 - 查看 | ✅                   | ✅        |
| 系统管理 - 访问 | ❌                   | ✅        |

### 7.2 全文检索字段说明

| 检索场景     | 检索字段                                                 | 数据来源表                         |
| ------------ | -------------------------------------------------------- | ---------------------------------- |
| 项目日志检索 | 项目名称、项目号、工作内容、客户简称、客户名称、员工姓名 | 工作日志表、项目表、客户表、员工表 |
| 项目信息检索 | 项目名称、项目号、客户简称、客户名称                     | 项目表、客户表                     |

### 7.3 数据库建表语句

```sql
-- 客户信息表：存储客户的基本信息，包括名称、简称等，支持客户信息的管理与检索
CREATE TABLE `customer` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(200) NOT NULL COMMENT '客户名称',
  `abbreviation` varchar(100) NOT NULL COMMENT '客户简称',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `office_address` varchar(500) DEFAULT NULL COMMENT '办公地址',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_name` (`name`) USING BTREE COMMENT '客户名称唯一',
  UNIQUE KEY `uk_customer_abbreviation` (`abbreviation`) USING BTREE COMMENT '客户简称唯一',
  -- 支持客户名称和简称的全文检索，满足项目查询需求
  FULLTEXT INDEX `ft_customer_search` (`name`, `abbreviation`) COMMENT '客户信息全文检索索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户信息表';

-- 部门信息表：记录公司部门结构，支持部门层级关系，便于组织管理
CREATE TABLE `department` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `parent_id` int DEFAULT NULL COMMENT '父部门ID（自关联，顶级部门为NULL）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_department_name` (`name`) USING BTREE COMMENT '部门名称唯一',
  KEY `idx_department_parent_id` (`parent_id`) USING BTREE COMMENT '父部门ID索引，优化部门层级查询',
  CONSTRAINT `fk_department_parent` FOREIGN KEY (`parent_id`) 
    REFERENCES `department` (`id`) 
    ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门信息表';

-- 员工信息表：存储员工的基本信息、登录账号及所属部门等，支持员工身份管理和权限控制
CREATE TABLE `employee` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `login_name` varchar(128) NOT NULL COMMENT '登录账号（使用email）',
  `display_name` varchar(128) NOT NULL COMMENT '员工姓名',
  `password` varchar(128) NOT NULL COMMENT '登录密码（哈希存储）',
  `employee_no` varchar(50) NOT NULL COMMENT '员工编号',
  `is_sales` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为销售人员（0否 1是）',
  `is_admin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为管理员（0否 1是）',
  `department_id` int NOT NULL COMMENT '所属部门ID（关联部门信息表）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '员工状态（1在职 2离职）',
  `is_first_login` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否首次登录（1是 0否），用于强制修改初始密码',
  `last_password_change` datetime DEFAULT NULL COMMENT '最后一次密码修改时间',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_login_name` (`login_name`) USING BTREE COMMENT '登录账号唯一（用于邮件找回密码）',
  UNIQUE KEY `uk_employee_no` (`employee_no`) USING BTREE COMMENT '员工编号唯一',
  KEY `idx_employee_department_id` (`department_id`) USING BTREE COMMENT '部门ID索引，优化部门员工查询',
  KEY `idx_employee_status` (`status`) USING BTREE COMMENT '员工状态索引，优化在职/离职员工筛选',
  -- 支持员工姓名的全文检索，满足项目日志查询需求
  FULLTEXT INDEX `ft_employee_name` (`display_name`) COMMENT '员工姓名全文检索索引',
  CONSTRAINT `fk_employee_department` FOREIGN KEY (`department_id`) 
    REFERENCES `department` (`id`) 
    ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='员工信息表';

-- 项目信息表：记录项目的基本信息、所属客户及负责人等，支持项目全生命周期管理
CREATE TABLE `project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_number` varchar(50) NOT NULL COMMENT '项目号或合同号',
  `project_type` enum('商机占位','合同项目','运维项目','其他类型') NOT NULL COMMENT '项目类型',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `customer_id` int NOT NULL COMMENT '所属客户ID（关联客户信息表）',
  `manager_id` int NOT NULL COMMENT '项目负责人ID（关联员工信息表）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '项目状态（1进行中 2已结束）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_number` (`project_number`) USING BTREE COMMENT '项目号唯一',
  KEY `idx_project_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引，优化客户项目查询',
  KEY `idx_project_manager_id` (`manager_id`) USING BTREE COMMENT '负责人ID索引，优化负责人项目查询',
  KEY `idx_project_status` (`status`) USING BTREE COMMENT '项目状态索引，优化项目状态筛选',
  -- 支持项目名称和项目号的全文检索，满足项目日志和项目查询需求
  FULLTEXT INDEX `ft_project_search` (`project_name`, `project_number`) COMMENT '项目信息全文检索索引',
  CONSTRAINT `fk_project_customer` FOREIGN KEY (`customer_id`) 
    REFERENCES `customer` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `fk_project_manager` FOREIGN KEY (`manager_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目信息表';

-- 工作日志表：存储员工每日工作记录，包括工作内容、工时及关联项目等，支持工作统计与追溯
CREATE TABLE `worklog` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '工作日志ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `employee_id` int NOT NULL COMMENT '所属员工ID（关联员工表）',
  `project_id` int NOT NULL COMMENT '所属项目ID（关联项目表）',
  `work_hours` decimal(3,1) NOT NULL COMMENT '工时（0-24之间，精度0.5）',
  `work_content` text NOT NULL COMMENT '工作内容',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_worklog_unique` (`work_date`,`employee_id`,`project_id`) USING BTREE COMMENT '确保同一天内同一员工的同一项目日志唯一',
  KEY `idx_worklog_employee_date` (`employee_id`,`work_date`) USING BTREE COMMENT '员工+日期组合索引，优化个人日志查询',
  KEY `idx_worklog_project_date` (`project_id`,`work_date`) USING BTREE COMMENT '项目+日期组合索引，优化项目日志查询',
  KEY `idx_worklog_work_date` (`work_date`) USING BTREE COMMENT '工作日期索引，优化日期范围查询',
  -- 支持工作内容的全文检索，满足项目日志查询需求
  FULLTEXT INDEX `ft_worklog_content` (`work_content`) COMMENT '工作日志内容全文检索索引',
  CONSTRAINT `fk_worklog_employee` FOREIGN KEY (`employee_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `fk_worklog_project` FOREIGN KEY (`project_id`) 
    REFERENCES `project` (`id`) 
    ON DELETE RESTRICT,
  CONSTRAINT `chk_work_hours` CHECK (
    (`work_hours` >= 0) AND 
    (`work_hours` <= 24) AND 
    (`work_hours` % 0.5 = 0)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工作日志表';

-- 加班记录表：存储员工加班信息，关联工作日志或独立记录
CREATE TABLE `overtime` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '加班ID',
  `employee_id` int NOT NULL COMMENT '员工ID（关联员工表）',
  `start_time` datetime NOT NULL COMMENT '加班开始时间（精确到年月日分钟）',
  `end_time` datetime NOT NULL COMMENT '加班结束时间（精确到年月日分钟）',
  `overtime_hours` decimal(4,1) GENERATED ALWAYS AS ((timestampdiff(minute,`start_time`,`end_time`)/60)) STORED COMMENT '加班时长（自动计算，单位小时）',
  `project_id` int NOT NULL COMMENT '所属项目ID（关联项目表）',
  `overtime_type` tinyint NOT NULL DEFAULT 1 COMMENT '加班类型（1：工作日加班；2：周末加班；3：法定节假日加班）',
  `overtime_reasons` text NOT NULL COMMENT '加班原因',
  `remarks` varchar(500) DEFAULT NULL COMMENT '加班备注（如“在哪里加班?”）',
  `approver_id` int DEFAULT NULL COMMENT '审批人ID（关联员工表）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记（0正常 1删除）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  -- 修改唯一键约束，使用DATE(start_time)代替overtime_date
  UNIQUE KEY `uk_overtime_unique` (`employee_id`,(DATE(`start_time`)),`start_time`,`end_time`) USING BTREE COMMENT '避免同一时段重复记录',
  -- 修改索引，使用DATE(start_time)代替overtime_date
  KEY `idx_overtime_employee_date` (`employee_id`,(DATE(`start_time`))) USING BTREE COMMENT '员工+日期索引，优化查询',
  KEY `idx_overtime_project_date` (`project_id`, (DATE(`start_time`))) USING BTREE COMMENT '项目+日期索引，优化项目加班统计',
  CONSTRAINT `fk_overtime_employee` FOREIGN KEY (`employee_id`) REFERENCES `employee` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_overtime_project` FOREIGN KEY (`project_id`) REFERENCES `project` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_overtime_approver` FOREIGN KEY (`approver_id`) REFERENCES `employee` (`id`) ON DELETE SET NULL,
  CONSTRAINT `chk_overtime_time` CHECK ((`end_time` > `start_time`)),
  -- 删除引用不存在列的约束，或修改为使用现有列
  CONSTRAINT `chk_overtime_hours_limit` CHECK ((`overtime_hours` > 0) AND (`overtime_hours` <= 24))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='加班记录表（包含约束：结束时间必须晚于开始时间）';

-- 密码重置表：用于邮件找回密码功能
CREATE TABLE `password_reset` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `employee_id` int NOT NULL COMMENT '员工ID（关联员工表）',
  `reset_token` varchar(255) NOT NULL COMMENT '密码重置令牌（唯一）',
  `expires_at` datetime NOT NULL COMMENT '令牌过期时间',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用（0未使用 1已使用）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_reset_token` (`reset_token`) USING BTREE COMMENT '重置令牌唯一',
  KEY `idx_employee_id` (`employee_id`) USING BTREE COMMENT '员工ID索引，优化查询',
  KEY `idx_token_expire` (`reset_token`,`expires_at`) USING BTREE COMMENT '令牌+过期时间索引，验证令牌有效性',
  CONSTRAINT `fk_password_reset_employee` FOREIGN KEY (`employee_id`) 
    REFERENCES `employee` (`id`) 
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='密码重置记录表';

-- 系统管理表：存储系统配置参数
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键（唯一）',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_desc` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（0禁用 1启用）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`) USING BTREE COMMENT '配置键唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表';
```

## 八、开发环境启动

### 8.1 数据库配置

- 地址：**************
- 端口：3306
- 数据库名：worklog_system
- 用户名：root
- 密码：Jcinfo#1995

### 8.2 开发环境启动

#### 8.2.1 后端

```bash
cd backend
uv sync
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 8.2.2 前端

```bash
cd frontend
npm install
npm run dev
```

#### 8.2.3 访问

前端：http://localhost:3000

后端API：http://localhost:8000

API文档：http://localhost:8000/docs

